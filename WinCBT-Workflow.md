﻿flowchart TD
    A[Operator Enters Roll Number] --> B[Click Search Button]
    B --> C[System Resets Interface]
    C --> D[Load Candidate Data]
    
    D --> E{Candidate Exists?}
    E -->|No| F[Show "Candidate Not Found" in Red]
    E -->|Yes| G{Candidate Active?}
    
    G -->|No| H[Show "Not Active" in Red]
    G -->|Yes| I{Check BiometricStatus}
    
    I -->|BiometricStatus = "Verified"| J{Has Seat Assignment?}
    I -->|BiometricStatus ≠ "Verified"| K[Fresh/Incomplete Candidate]
    
    J -->|Yes| L[Already Assigned Scenario]
    J -->|No| M[Verified but No Seat Scenario]
    
    %% Already Assigned Scenario
    L --> L1[Show "Seat already assigned" in Green]
    L1 --> L2[Display Seat Number in Green]
    L2 --> L3[Disable Capture Group]
    L3 --> L4[Show Verification Status as Completed]
    L4 --> L5[Hide Assign Seat Button]
    L5 --> L6[Set Camera Inactive]
    L6 --> END1[End - Ready for New Search]
    
    %% Verified but No Seat Scenario
    M --> M1[Show Candidate Name in Green]
    M1 --> M2[Show All Status as Verified]
    M2 --> M3[Disable Capture Group]
    M3 --> M4[Enable Assign Seat Button]
    M4 --> M5[Set Camera Inactive]
    M5 --> SEAT[Go to Seat Assignment]
    
    %% Fresh/Incomplete Candidate Scenario
    K --> K1[Show Candidate Name in Black]
    K1 --> K2[Enable Capture Group]
    K2 --> K3[Enable Verification Group]
    K3 --> K4[Activate Camera Feed]
    K4 --> K5[Show All Status as Pending '-']
    K5 --> VERIFY[Start Verification Process]
    
    %% Verification Process
    VERIFY --> V1[Photo Verification]
    V1 --> V1A{User Clicks Capture Photo?}
    V1A -->|Yes| V1B[Capture Photo from Webcam]
    V1B --> V1C[Compare with Registered Photo]
    V1C --> V1D{Confidence ≥ Threshold?}
    
    V1D -->|Yes| V1E[Auto-Verify: Photo Status = "Verified" Green]
    V1D -->|No| V1F[Manual Review: Photo Status = "Needs Review" Red]
    V1F --> V1G[Enable Verify Picture Button]
    V1G --> V1H{Manual Verification?}
    V1H -->|Approved| V1E
    V1H -->|Rejected| V1I[Photo Status = "Failed" Red]
    
    V1E --> V2[Fingerprint Verification]
    V1I --> V1A
    
    %% Fingerprint Verification
    V2 --> V2A{Special Candidate?}
    V2A -->|Yes| V2B[Show Thumb Selection Dialog]
    V2B --> V2C{Thumb Selection}
    V2C -->|Left Only| V2D[Enable Left Thumb Capture Only]
    V2C -->|Right Only| V2E[Enable Right Thumb Capture Only]
    V2C -->|Both Thumbs| V2F[Enable Both Thumb Captures]
    
    V2A -->|No| V2G[Standard Fingerprint Flow]
    V2G --> V2H{Right Thumb Enabled Globally?}
    V2H -->|Yes| V2F
    V2H -->|No| V2D
    
    %% Left Thumb Capture
    V2D --> V3A{User Clicks Capture Left Thumb?}
    V3A -->|Yes| V3B[Activate Fingerprint Reader LED]
    V3B --> V3C[Capture Fingerprint Template]
    V3C --> V3D{Quality ≥ 80?}
    V3D -->|Yes| V3E[Left Fingerprint Status = "Saved" Green]
    V3D -->|No| V3F[Show Quality Error]
    V3F --> V3A
    
    %% Right Thumb Capture (if enabled)
    V2E --> V4A{User Clicks Capture Right Thumb?}
    V2F --> V3E
    V3E --> V4B{Right Thumb Required?}
    V4B -->|Yes| V4A
    V4B -->|No| V5[Signature Verification]
    
    V4A -->|Yes| V4C[Activate Right Fingerprint Reader]
    V4C --> V4D[Capture Right Fingerprint Template]
    V4D --> V4E{Quality ≥ 80?}
    V4E -->|Yes| V4F[Right Fingerprint Status = "Saved" Green]
    V4E -->|No| V4G[Show Quality Error]
    V4G --> V4A
    V4F --> V5
    
    %% Signature Verification
    V5 --> V5A{Signature Verification Enabled?}
    V5A -->|No| V5B[Signature Status = "Disabled" Gray]
    V5A -->|Yes| V5C{User Clicks Capture Signature?}
    V5C -->|Yes| V5D[Capture Signature]
    V5D --> V5E[Compare with Registered Signature]
    V5E --> V5F{Verification Successful?}
    V5F -->|Yes| V5G[Signature Status = "Verified" Green]
    V5F -->|No| V5H[Signature Status = "Failed" Red]
    V5H --> V5C
    
    V5B --> CHECK[Check All Verifications]
    V5G --> CHECK
    
    %% Verification Completion Check
    CHECK --> C1{All Required Verifications Complete?}
    C1 -->|Photo: Verified AND| C2{Fingerprint: Saved AND}
    C2 -->|Signature: Verified/Disabled| C3[All Complete]
    C1 -->|Missing Verifications| C4[Incomplete]
    C2 -->|Missing Fingerprint| C4
    
    C3 --> C3A[Verification Status = "Completed" Green]
    C3A --> C3B[Post Verification Status = "Completed" Green]
    C3B --> C3C[Enable Assign Seat Button]
    C3C --> C3D[Disable All Capture Buttons]
    C3D --> C3E[Update BiometricStatus = "Verified" in Database]
    C3E --> SEAT
    
    C4 --> C4A[Verification Status = "Incomplete" Red]
    C4A --> C4B[Keep Assign Seat Button Disabled]
    C4B --> VERIFY
    
    %% Seat Assignment Process
    SEAT --> S1{User Clicks Assign Seat?}
    S1 -->|Yes| S2[Show "Assigning seat..." in Status Bar]
    S2 --> S3[Search Available Seats in Room]
    S3 --> S4{Seat Available?}
    
    S4 -->|Yes| S5[Allocate Seat Using Algorithm]
    S5 --> S6[Update Database with Assignment]
    S6 --> S7[Show Seat Assignment Popup]
    S7 --> S8[Update Assigned Seat Field Green]
    S8 --> S9[Reset Interface for Next Candidate]
    S9 --> END2[End - Ready for New Search]
    
    S4 -->|No| S10[Show "No Available Seats" Error]
    S10 --> S11[Keep Assign Seat Button Enabled]
    S11 --> S1
    
    %% Error Handling Flows
    F --> END3[End - Error State]
    H --> END4[End - Inactive Candidate]
    
    %% Hardware Error Flows
    V1B --> HW1{Camera Working?}
    HW1 -->|No| HW2[Show "Camera Error"]
    HW2 --> HW3[Footer: "Camera: Not Connected" Red]
    HW3 --> END5[End - Hardware Error]
    
    V3C --> HW4{Fingerprint Reader Working?}
    HW4 -->|No| HW5[Show "Fingerprint Reader Error"]
    HW5 --> HW6[Footer: "Fingerprint: Not Connected" Red]
    HW6 --> END6[End - Hardware Error]
    
    %% Styling
    classDef successState fill:#d4edda,stroke:#155724,color:#155724
    classDef errorState fill:#f8d7da,stroke:#721c24,color:#721c24
    classDef processState fill:#cce5ff,stroke:#004085,color:#004085
    classDef decisionState fill:#fff3cd,stroke:#856404,color:#856404
    classDef endState fill:#e2e3e5,stroke:#383d41,color:#383d41
    
    class C3A,C3B,V1E,V3E,V4F,V5G,L1,L2,M1,M2,S7,S8 successState
    class F,H,V1I,V3F,V4G,V5H,C4A,S10,HW2,HW5 errorState
    class A,B,C,D,K1,K2,K3,K4,V1B,V3B,V4C,S2,S3,S5,S6 processState
    class E,G,I,J,V1D,V1H,V2A,V2H,V3D,V4E,V5F,C1,C2,S1,S4,HW1,HW4 decisionState
    class END1,END2,END3,END4,END5,END6 endState