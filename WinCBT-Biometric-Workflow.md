# WinCBT-Biometric Pre-Exam Verification Workflow

```mermaid
flowchart TD
    A[Enter Roll Number] --> B[Click Search Button]
    B --> C[Reset Interface & Load Data]
    C --> D{Candidate Found?}

    D -->|No| E[Show Not Found - Red]
    D -->|Yes| F{Active Candidate?}

    F -->|No| G[Show Not Active - Red]
    F -->|Yes| H{Check BiometricStatus}

    H -->|Verified| I{Has Seat?}
    H -->|Not Verified| J[Fresh Candidate Flow]

    %% Already Processed Scenarios
    I -->|Yes| K[Seat Already Assigned]
    I -->|No| L[Verified No Seat]

    K --> K1[Show Seat already assigned - Green]
    K1 --> K2[Display Seat Number]
    K2 --> K3[Disable All Capture Controls]
    K3 --> K4[Hide Assign Seat Button]
    K4 --> END1[Ready for Next Search]

    L --> L1[Show Name - Green]
    L1 --> L2[Show All Status Verified]
    L2 --> L3[Disable Capture Controls]
    L3 --> L4[Enable Assign Seat Button]
    L4 --> ASSIGN[Seat Assignment Flow]

    %% Fresh Candidate Verification
    J --> J1[Show Name - Black]
    J1 --> J2[Enable Capture Group]
    J2 --> J3[Activate Camera]
    J3 --> J4[All Status Show Dash]
    J4 --> SPECIAL_CHECK{Special Candidate?}

    %% Special Candidate Immediate Dialog
    SPECIAL_CHECK -->|Yes| SP1[Show Special Case Indicator]
    SP1 --> SP2[Show Thumb Selection Dialog IMMEDIATELY]
    SP2 --> SP3{Dialog Selection}
    SP3 -->|Left Only| SP4[Enable Left Capture Only]
    SP3 -->|Right Only| SP5[Enable Right Capture Only]
    SP3 -->|Both Thumbs| SP6[Enable Both Captures]
    SP4 --> SP7[Hide All Thumb Controls]
    SP5 --> SP7
    SP6 --> SP7
    SP7 --> PHOTO[Photo Verification]

    %% Non-Special Candidate
    SPECIAL_CHECK -->|No| NS1[Show Standard Thumb Controls]
    NS1 --> PHOTO

    %% Photo Verification Flow
    PHOTO --> P1{Click Capture Photo?}
    P1 -->|Yes| P2[Capture from Webcam]
    P2 --> P3[Compare with Registered]
    P3 --> P4{Confidence >= 70%?}

    P4 -->|Yes| P5[Auto-Verify - Green Verified]
    P4 -->|No| P6[Manual Review - Red Needs Review]
    P6 --> P7{Manual Approve?}
    P7 -->|Yes| P5
    P7 -->|No| P8[Failed - Red]
    P8 --> P1

    P5 --> FINGER[Fingerprint Verification]

    %% Fingerprint Verification Flow
    %% Note: Special candidate thumb selection already handled after search
    FINGER --> F1{Right Thumb Enabled for this Candidate?}
    F1 -->|Yes - Both Required| F2[Both Thumb Captures Available]
    F1 -->|No - Left Only| F3[Left Thumb Capture Only]

    F2 --> L_THUMB[Left Thumb Capture]
    F3 --> L_THUMB

    L_THUMB --> LT1{Click Left Capture?}
    LT1 -->|Yes| LT2[Activate Reader LED]
    LT2 --> LT3[Capture Template]
    LT3 --> LT4{Quality >= 80?}
    LT4 -->|Yes| LT5[Left Status Saved - Green]
    LT4 -->|No| LT6[Quality Error]
    LT6 --> LT1

    LT5 --> RT_CHECK{Right Thumb Required?}
    RT_CHECK -->|Yes| R_THUMB
    RT_CHECK -->|No| SIG[Signature Verification]

    R_THUMB --> RT1{Click Right Capture?}
    RT1 -->|Yes| RT2[Activate Right Reader]
    RT2 --> RT3[Capture Right Template]
    RT3 --> RT4{Quality >= 80?}
    RT4 -->|Yes| RT5[Right Status Saved - Green]
    RT4 -->|No| RT6[Quality Error]
    RT6 --> RT1

    RT5 --> SIG

    %% Signature Verification
    SIG --> S1{Signature Enabled?}
    S1 -->|No| S2[Status Disabled - Gray]
    S1 -->|Yes| S3{Click Capture Signature?}
    S3 -->|Yes| S4[Capture Signature]
    S4 --> S5{Verification OK?}
    S5 -->|Yes| S6[Status Verified - Green]
    S5 -->|No| S7[Status Failed - Red]
    S7 --> S3

    S2 --> CHECK[Check All Verifications]
    S6 --> CHECK

    %% Verification Completion Check
    CHECK --> C1{All Complete?}
    C1 -->|All Verified| C2[All Verified]
    C1 -->|Missing Any| C3[Incomplete]

    C2 --> C4[Verification Status Completed - Green]
    C4 --> C5[Enable Assign Seat Button]
    C5 --> C6[Disable Capture Buttons]
    C6 --> C7[Update Database BiometricStatus]
    C7 --> ASSIGN

    C3 --> C8[Verification Status Incomplete - Red]
    C8 --> C9[Keep Assign Seat Disabled]
    C9 --> PHOTO

    %% Seat Assignment Flow
    ASSIGN --> A1{Click Assign Seat?}
    A1 -->|Yes| A2[Show Assigning Status]
    A2 --> A3[Search Available Seats]
    A3 --> A4{Seat Available?}

    A4 -->|Yes| A5[Allocate Seat]
    A5 --> A6[Update Database]
    A6 --> A7[Show Success Popup]
    A7 --> A8[Reset Interface]
    A8 --> END2[Ready for Next Search]

    A4 -->|No| A9[Show No Seats Error]
    A9 --> A1

    %% Error End States
    E --> END3[Error State]
    G --> END4[Inactive State]

    %% Styling
    classDef success fill:#d4edda,stroke:#155724
    classDef error fill:#f8d7da,stroke:#721c24
    classDef process fill:#cce5ff,stroke:#004085
    classDef decision fill:#fff3cd,stroke:#856404
    classDef endpoint fill:#e2e3e5,stroke:#383d41

    class P5,LT5,RT5,S6,C4,A7,L1,SP4,SP5,SP6 success
    class E,G,P8,LT6,RT6,S7,C8,A9 error
    class A,B,C,J1,J2,P2,LT2,RT2,A2,A5,SP1,SP2,SP7,NS1 process
    class D,F,H,I,P4,F1,LT4,RT4,S5,C1,A4,SPECIAL_CHECK,SP3 decision
    class END1,END2,END3,END4 endpoint
```

## Workflow Summary

### Main Scenarios:
1. **Fresh Candidate**: Complete verification workflow
2. **Verified + No Seat**: Direct to seat assignment
3. **Already Assigned**: Read-only display
4. **Error Cases**: Not found or inactive

### Verification Steps:
1. **Photo**: Auto-verify if confidence >=70%, else manual review
2. **Fingerprint**: Quality >=80% required, special candidate handling
3. **Signature**: Optional based on configuration

### Completion Criteria:
- Photo: Verified status
- Fingerprint: Saved status (pre-exam mode)
- Signature: Verified or Disabled

### Visual Indicators:
- **Green**: Success/Completed states
- **Red**: Error/Failed states
- **Gray**: Disabled/Pending states
- **Blue**: Active/Processing states
