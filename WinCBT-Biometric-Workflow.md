# WinCBT-Biometric Pre-Exam Verification Workflow

```mermaid
flowchart TD
    A[Enter Roll Number] --> B[Click Search Button]
    B --> C[Reset Interface & Load Data]
    C --> D{Candidate Found?}

    D -->|No| E[Show Not Found - Red]
    D -->|Yes| F{Check BiometricStatus}

    %% Already Verified Scenarios
    F -->|Verified| G{Has Seat Assignment?}
    G -->|Yes| H[Already Completed]
    G -->|No| I[Verified - Ready for Seat]

    H --> H1[Show Seat already assigned - Green]
    H1 --> H2[Display Seat Number]
    H2 --> H3[Disable All Controls]
    H3 --> END1[Ready for Next Search]

    I --> I1[Show Name - Green]
    I1 --> I2[Show All Status Verified]
    I2 --> I3[Enable Assign Seat Button]
    I3 --> ASSIGN[Seat Assignment]

    %% Fresh Candidate Flow
    F -->|Not Verified| J[Fresh Candidate]
    J --> J1[Show Name - Black]
    J1 --> J2[Enable Capture & Verification Groups]
    J2 --> J3[Activate Camera]
    J3 --> J4[Set All Status to Dash]
    J4 --> J5{Special=1?}

    %% Special Candidate Handling
    J5 -->|Yes| S1[Show Special Indicator]
    S1 --> S2[Show Thumb Selection Dialog]
    S2 --> S3[Configure Buttons Based on Selection]
    S3 --> S4[Hide Standard Thumb Controls]
    S4 --> READY[Ready for Verification]

    %% Regular Candidate
    J5 -->|No| R1[Show Standard Thumb Controls]
    R1 --> READY

    %% Verification Process
    READY --> PHOTO[Photo Verification]

    %% Photo Verification
    PHOTO --> P1{Click Capture Photo?}
    P1 -->|Yes| P2[Capture & Auto-Verify]
    P2 --> P3{Confidence >= 70%?}
    P3 -->|Yes| P4[Photo Status: Verified]
    P3 -->|No| P5[Photo Status: Needs Review]
    P5 --> P6{Manual Approve?}
    P6 -->|Yes| P4
    P6 -->|No| P1

    P4 --> FINGER[Fingerprint Verification]

    %% Fingerprint Verification
    FINGER --> F1{Click Capture Fingerprint?}
    F1 -->|Yes| F2[Capture & Check Quality]
    F2 --> F3{Quality >= 80?}
    F3 -->|Yes| F4[Fingerprint Status: Saved]
    F3 -->|No| F5[Quality Error - Retry]
    F5 --> F1

    F4 --> F6{Right Thumb Required?}
    F6 -->|Yes| F7[Capture Right Thumb]
    F6 -->|No| SIG[Signature Check]

    F7 --> F8{Right Thumb Quality >= 80?}
    F8 -->|Yes| F9[Right Fingerprint: Saved]
    F8 -->|No| F7

    F9 --> SIG

    %% Signature Verification
    SIG --> S1{Signature Enabled?}
    S1 -->|No| S2[Signature: Disabled]
    S1 -->|Yes| S3{Click Capture Signature?}
    S3 -->|Yes| S4[Signature: Verified or Failed]
    S4 --> CHECK[Check All Complete]
    S2 --> CHECK

    %% Completion Check
    CHECK --> C1{All Verifications Complete?}
    C1 -->|Yes| C2[Verification Status: Completed]
    C1 -->|No| C3[Verification Status: Incomplete]

    C2 --> C4[Enable Assign Seat Button]
    C4 --> ASSIGN
    C3 --> PHOTO

    %% Seat Assignment
    ASSIGN --> A1{Click Assign Seat?}
    A1 -->|Yes| A2[Assign Seat & Show Popup]
    A2 --> A3[Reset Interface]
    A3 --> END2[Ready for Next Search]

    %% Error States
    E --> END3[Error State]

    %% Styling
    classDef success fill:#d4edda,stroke:#155724
    classDef error fill:#f8d7da,stroke:#721c24
    classDef process fill:#cce5ff,stroke:#004085
    classDef decision fill:#fff3cd,stroke:#856404
    classDef endpoint fill:#e2e3e5,stroke:#383d41

    class P4,F4,F9,S2,S4,C2,I1,I2,H1,H2,A2 success
    class E,P5,F5,C3 error
    class A,B,C,J1,J2,J3,J4,S1,S3,R1,P2,F2,F7,A3 process
    class D,F,G,J5,P1,P3,P6,F1,F3,F6,F8,S1,C1,A1 decision
    class END1,END2,END3 endpoint
```

## Workflow Summary

### Main Scenarios:
1. **Fresh Candidate**: Complete verification workflow
2. **Verified + No Seat**: Direct to seat assignment
3. **Already Assigned**: Read-only display
4. **Error Cases**: Not found or inactive

### Verification Steps:
1. **Photo**: Auto-verify if confidence >=70%, else manual review
2. **Fingerprint**: Quality >=80% required, special candidate handling
3. **Signature**: Optional based on configuration

### Completion Criteria:
- Photo: Verified status
- Fingerprint: Saved status (pre-exam mode)
- Signature: Verified or Disabled

### Visual Indicators:
- **Green**: Success/Completed states
- **Red**: Error/Failed states
- **Gray**: Disabled/Pending states
- **Blue**: Active/Processing states
